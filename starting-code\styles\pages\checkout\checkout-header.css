.checkout-header {
  height: 60px;
  padding-left: 30px;
  padding-right: 30px;
  background-color: white;

  display: flex;
  justify-content: center;

  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.checkout-header .header-content {
  width: 100%;
  max-width: 1100px;

  display: flex;
  align-items: center;
}

.checkout-header .checkout-header-left-section {
  width: 200px;
}

.checkout-header .logo {
  height: 26px;
  margin-top: 0px;
}

.checkout-header .mobile-logo {
  display: none;
}

/* @media is used to create responsive design (making the
   website look good on any screen size). This @media
   means when the screen width is 575px or less, different
   styles (inside the {...}) will be applied. */
@media (max-width: 575px) {
  .checkout-header .checkout-header-left-section {
    width: auto;
  }

  .checkout-header .logo {
    display: none;
  }

  .checkout-header .mobile-logo {
    display: inline-block;
    height: 26px;
  }
}

.checkout-header .checkout-header-middle-section {
  flex: 1;
  flex-shrink: 0;
  text-align: center;

  font-size: 22px;
  font-weight: 500;

  display: flex;
  justify-content: center;
}

.checkout-header .return-to-home-link {
  color: rgb(25, 135, 84);

  text-decoration: none;
  cursor: pointer;
}

@media (max-width: 1000px) {
  .checkout-header .checkout-header-middle-section {
    font-size: 20px;
    margin-right: 60px;
  }

  .checkout-header .return-to-home-link {
    font-size: 20px;
  }
}

@media (max-width: 575px) {
  .checkout-header .checkout-header-middle-section {
    margin-right: 5px;
  }
}

.checkout-header .checkout-header-right-section {
  text-align: right;
  width: 200px;
  display: flex;
  align-items: center;
  justify-content: end;
}

.checkout-header .checkout-header-right-section img {
  height: 32px;
}

@media (max-width: 1000px) {
  .checkout-header .checkout-header-right-section {
    width: auto;
  }
}