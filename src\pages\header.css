.header {
  background-color: rgb(8, 79, 45);
  color: white;
  padding-left: 15px;
  padding-right: 15px;

  display: flex;
  align-items: center;
  justify-content: space-between;

  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
}

.header .left-section {
  width: 208px;
}

@media (max-width: 800px) {
  .header .left-section {
    width: unset;
  }
}

.header .header-link {
  display: inline-block;
  padding: 6px 9.5px;
  border-radius: 2px;
  cursor: pointer;
  text-decoration: none;
  border: 1px solid rgba(0, 0, 0, 0);
}

.header .header-link:hover {
  border: 1px solid white;
}

.header .logo {
  height: 26px;
  margin-top: 1px;
}

.header .mobile-logo {
  display: none;
}

@media (max-width: 675px) {
  .header .logo {
    display: none;
  }

  .header .mobile-logo {
    display: block;
    height: 26px;
    margin-top: 1px;
  }
}

.header .middle-section {
  flex: 1;
  max-width: 850px;
  margin-left: 10px;
  margin-right: 10px;
  display: flex;
}

.header .search-bar {
  flex: 1;
  width: 0;
  font-size: 16px;
  height: 38px;
  padding-left: 15px;
  border: none;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.header .search-button {
  background-color: rgb(186, 255, 190);
  border: none;
  width: 45px;
  height: 40px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  flex-shrink: 0;
}

.header .search-icon {
  height: 20px;
  margin-left: 0px;
  margin-top: 3px;
}

.header .right-section {
  width: 180px;
  flex-shrink: 0;
  display: flex;
  justify-content: end;
}

.header .orders-link {
  color: white;

  display: flex;
  align-items: center;
  padding-left: 13px;
  padding-right: 13px;
}

.header .orders-text {
  display: block;
  font-size: 15px;
  font-weight: 700;
}

.header .cart-link {
  color: white;
  display: flex;
  align-items: center;
  position: relative;
}

.header .cart-icon {
  width: 38px;
}

.header .cart-text {
  margin-left: 5px;
  font-size: 15px;
  font-weight: 700;
}

.header .cart-quantity {
  color: rgb(8, 79, 45);
  font-size: 14px;
  font-weight: 700;

  position: absolute;
  top: 8.5px;
  right: 46px;

  width: 26px;
  text-align: center;
}