body {
  font-family: <PERSON><PERSON>, <PERSON><PERSON>;
  color: rgb(33, 33, 33);
  /* The <body> element has a default margin of 8px
     on all sides. This removes the default margins. */
  margin: 0;
}

/* Remove the default margins of <p> elements. */
p {
  margin: 0;
}

button {
  font-size: 14px;
  cursor: pointer;
}

select {
  cursor: pointer;
}

input, select, button {
  font-family: Robot<PERSON>, Arial;
}

.button-primary {
  color: rgb(255, 255, 255);
  background-color: rgb(25, 135, 84);
  border: 1px solid transparent;
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(220, 220, 220, 0.5);
}

.button-primary:hover {
  background-color: rgba(25, 135, 84, 0.75);
  border: 1px solid transparent;
}

.button-primary:active {
  background: rgba(25, 135, 84, 0.5);
  border-color: transparent;
  box-shadow: none;
}

.button-secondary {
  color: rgb(33, 33, 33);
  background: white;
  border: 1px solid rgb(200, 200, 200);
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(220, 220, 220, 0.2);
}

.button-secondary:hover {
  background-color: rgb(250, 250, 250);
}

.button-secondary:active {
  background-color: rgb(240, 240, 240);
  box-shadow: none;
}

/* These styles will limit text to 2 lines. Anything
   beyond 2 lines will be replaced with "..."
   You can find this code by using an A.I. tool or by
   searching in Google.
   https://css-tricks.com/almanac/properties/l/line-clamp/ */
.limit-text-to-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.link-primary {
  color: rgb(25, 135, 84);
  cursor: pointer;
}

.link-primary:hover {
  opacity: 0.75;
}

.link-primary:active {
  opacity: 0.5;
}

/* Styles for dropdown selectors. */
select {
  color: rgb(33, 33, 33);
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(200, 200, 200);
  border-radius: 5px;
  padding: 3px 5px;
  font-size: 15px;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(200, 200, 200, 0.2);
}

select:focus,
input:focus {
  outline: 2px solid rgb(25, 135, 84);
}